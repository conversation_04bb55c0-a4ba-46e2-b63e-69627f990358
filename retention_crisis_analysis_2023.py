#!/usr/bin/env python3
"""
Analysis Report: 2023 Student Retention Crisis
Comprehensive analysis of the dramatic retention drop in 2023 cohort
"""

import pandas as pd
import json
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import numpy as np

class RetentionCrisisAnalyzer:
    """Analyzer for the 2023 retention crisis"""
    
    def __init__(self):
        self.csv_data = None
        self.api_data = None
        self.report_data = None
        
    def load_data(self):
        """Load all available data sources"""
        try:
            # Load CSV data
            self.csv_data = pd.read_csv("casos_laboratorio_clinico.csv", sep=';')
            
            # Load API data
            with open("api_response.json", 'r', encoding='utf-8') as f:
                self.api_data = json.load(f)
            
            # Load retention report
            with open("student_retention_report_20250820_113722.json", 'r', encoding='utf-8') as f:
                self.report_data = json.load(f)
                
            print("✅ All data sources loaded successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return False
    
    def analyze_2023_crisis(self):
        """Comprehensive analysis of the 2023 retention crisis"""
        
        print("\n" + "="*80)
        print("🚨 CRITICAL ANALYSIS: 2023 STUDENT RETENTION CRISIS")
        print("="*80)
        
        # Extract key findings from the data
        cohort_data = self.report_data['detailed_analysis']['cohort_analysis']
        
        print("\n📊 COHORT COMPARISON:")
        print("-" * 50)
        
        for year, data in cohort_data.items():
            ret1_rate = data['retention_rates']['RET_1']
            student_count = data['count']
            print(f"{year}: {student_count} students, RET_1: {ret1_rate:.1f}%")
        
        # Calculate the crisis impact
        crisis_impact = self.calculate_crisis_impact()
        
        print(f"\n🔍 CRISIS IMPACT ANALYSIS:")
        print("-" * 50)
        print(f"2023 Cohort Size: {crisis_impact['cohort_2023_size']} students")
        print(f"2023 RET_1 Rate: {crisis_impact['ret1_2023']:.1f}%")
        print(f"2024 RET_1 Rate: {crisis_impact['ret1_2024']:.1f}%")
        print(f"Retention Drop: {crisis_impact['retention_drop']:.1f} percentage points")
        print(f"Students Lost: {crisis_impact['students_lost']} out of {crisis_impact['cohort_2023_size']}")
        
        # Analyze 2023 student outcomes
        outcomes_2023 = self.analyze_2023_outcomes()
        
        print(f"\n📈 2023 COHORT FINAL OUTCOMES:")
        print("-" * 50)
        for outcome, count in outcomes_2023.items():
            percentage = (count / crisis_impact['cohort_2023_size']) * 100
            print(f"{outcome}: {count} students ({percentage:.1f}%)")
        
        return crisis_impact, outcomes_2023
    
    def calculate_crisis_impact(self):
        """Calculate the numerical impact of the 2023 crisis"""
        cohort_data = self.report_data['detailed_analysis']['cohort_analysis']
        
        # Extract data
        cohort_2023 = cohort_data.get('2023', {})
        cohort_2024 = cohort_data.get('2024', {})
        
        cohort_2023_size = cohort_2023.get('count', 0)
        ret1_2023 = cohort_2023.get('retention_rates', {}).get('RET_1', 0)
        ret1_2024 = cohort_2024.get('retention_rates', {}).get('RET_1', 0)
        
        retention_drop = ret1_2024 - ret1_2023
        students_lost = cohort_2023_size - int((ret1_2023/100) * cohort_2023_size)
        
        return {
            'cohort_2023_size': cohort_2023_size,
            'cohort_2024_size': cohort_2024.get('count', 0),
            'ret1_2023': ret1_2023,
            'ret1_2024': ret1_2024,
            'retention_drop': retention_drop,
            'students_lost': students_lost
        }
    
    def analyze_2023_outcomes(self):
        """Analyze what happened to 2023 cohort students"""
        # Filter 2023 students from at-risk data
        at_risk_students = self.report_data.get('at_risk_students', [])
        
        outcomes_2023 = {
            'Abandono de Estudios': 0,
            'Renuncia': 0,
            'Eliminado': 0,
            'No Activo': 0,
            'Retiro Temporal': 0
        }
        
        for student in at_risk_students:
            if student.get('AÑO_PROCESO_SIES') == 2023:
                outcome = student.get('estado_texto_mapped', 'Unknown')
                if outcome in outcomes_2023:
                    outcomes_2023[outcome] += 1
        
        return outcomes_2023
    
    def identify_root_causes(self):
        """Identify potential root causes of the 2023 crisis"""
        
        print(f"\n🔍 ROOT CAUSE ANALYSIS:")
        print("="*60)
        
        causes = {
            "1. SYSTEMIC FACTORS": [
                "• COVID-19 pandemic aftermath effects on 2023 cohort",
                "• Economic instability affecting student finances",
                "• Changes in labor market demand for technical careers",
                "• Possible changes in admission criteria or processes"
            ],
            
            "2. INSTITUTIONAL FACTORS": [
                "• Potential changes in program structure or curriculum in 2023",
                "• Faculty changes or resource limitations",
                "• Student support services gaps",
                "• Infrastructure or technology issues"
            ],
            
            "3. STUDENT PROFILE CHANGES": [
                "• Different demographic composition in 2023 cohort",
                "• Lower academic preparedness levels",
                "• Changed expectations or career goals",
                "• Financial hardship affecting study continuation"
            ],
            
            "4. PROGRAM-SPECIFIC ISSUES": [
                "• Laboratory Clinical program may have faced specific challenges",
                "• Industry changes affecting career prospects",
                "• Practical training limitations or delays",
                "• Certification or accreditation issues"
            ]
        }
        
        for category, factors in causes.items():
            print(f"\n{category}")
            print("-" * 40)
            for factor in factors:
                print(factor)
        
        return causes
    
    def generate_recommendations(self):
        """Generate specific recommendations to prevent future crises"""
        
        print(f"\n💡 STRATEGIC RECOMMENDATIONS:")
        print("="*60)
        
        recommendations = {
            "IMMEDIATE ACTIONS (0-3 months)": [
                "1. Conduct exit interviews with all 2023 dropouts to understand specific reasons",
                "2. Implement emergency retention support for current at-risk students",
                "3. Review and strengthen first-semester support programs",
                "4. Establish early warning system for academic and personal difficulties"
            ],
            
            "SHORT-TERM STRATEGIES (3-12 months)": [
                "1. Redesign orientation and onboarding process for new students",
                "2. Implement peer mentoring program pairing new students with successful ones",
                "3. Enhance financial aid and scholarship programs",
                "4. Strengthen career counseling and industry connection programs"
            ],
            
            "LONG-TERM INITIATIVES (1-3 years)": [
                "1. Develop predictive analytics for early identification of at-risk students",
                "2. Create flexible learning pathways and part-time options",
                "3. Establish industry partnerships for guaranteed internships/jobs",
                "4. Implement comprehensive student success tracking system"
            ],
            
            "MONITORING & EVALUATION": [
                "1. Monthly retention rate monitoring with immediate intervention triggers",
                "2. Quarterly student satisfaction and engagement surveys",
                "3. Annual program review with retention as key performance indicator",
                "4. Benchmark against similar technical programs nationally"
            ]
        }
        
        for category, actions in recommendations.items():
            print(f"\n{category}")
            print("-" * 50)
            for action in actions:
                print(action)
        
        return recommendations
    
    def create_crisis_visualization(self):
        """Create visualizations showing the 2023 crisis"""
        
        # Prepare data for visualization
        cohort_data = self.report_data['detailed_analysis']['cohort_analysis']
        
        years = list(cohort_data.keys())
        retention_rates = [cohort_data[year]['retention_rates']['RET_1'] for year in years]
        student_counts = [cohort_data[year]['count'] for year in years]
        
        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('2023 Student Retention Crisis Analysis', fontsize=16, fontweight='bold')
        
        # 1. Retention Rate Trend
        ax1.plot(years, retention_rates, marker='o', linewidth=3, markersize=10, color='red')
        ax1.set_title('RET_1 Retention Rate by Cohort', fontweight='bold')
        ax1.set_ylabel('Retention Rate (%)')
        ax1.set_ylim(0, 100)
        ax1.grid(True, alpha=0.3)
        
        # Highlight 2023 crisis
        crisis_idx = years.index('2023') if '2023' in years else None
        if crisis_idx is not None:
            ax1.scatter(years[crisis_idx], retention_rates[crisis_idx], 
                       color='red', s=200, zorder=5, label='Crisis Point')
            ax1.annotate('CRISIS!', xy=(years[crisis_idx], retention_rates[crisis_idx]), 
                        xytext=(10, 20), textcoords='offset points',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7),
                        arrowprops=dict(arrowstyle='->', color='red'))
        
        # 2. Student Count by Cohort
        colors = ['green' if year != '2023' else 'red' for year in years]
        ax2.bar(years, student_counts, color=colors, alpha=0.7)
        ax2.set_title('Student Count by Cohort', fontweight='bold')
        ax2.set_ylabel('Number of Students')
        
        # 3. 2023 Outcomes Breakdown
        outcomes_2023 = self.analyze_2023_outcomes()
        if outcomes_2023:
            labels = list(outcomes_2023.keys())
            values = list(outcomes_2023.values())
            colors_pie = ['#ff4444', '#ff8888', '#ffaaaa', '#ffcccc', '#ffeeee']
            
            ax3.pie(values, labels=labels, autopct='%1.1f%%', colors=colors_pie)
            ax3.set_title('2023 Cohort Final Outcomes', fontweight='bold')
        
        # 4. Risk Distribution Current vs Historical
        current_risk = self.report_data['detailed_analysis']['risk_categories']['percentages']
        risk_categories = list(current_risk.keys())
        risk_values = list(current_risk.values())
        
        ax4.bar(risk_categories, risk_values, color=['red', 'orange', 'green', 'blue', 'gray'])
        ax4.set_title('Current Risk Distribution', fontweight='bold')
        ax4.set_ylabel('Percentage of Students')
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('retention_crisis_2023_analysis.png', dpi=300, bbox_inches='tight')
        print(f"\n📊 Crisis visualization saved as 'retention_crisis_2023_analysis.png'")
        plt.show()
    
    def generate_executive_summary(self):
        """Generate executive summary for leadership"""
        
        crisis_impact, outcomes_2023 = self.analyze_2023_crisis()
        
        summary = f"""
EXECUTIVE SUMMARY: 2023 STUDENT RETENTION CRISIS
===============================================

CRISIS OVERVIEW:
• The 2023 cohort experienced a catastrophic retention failure
• RET_1 retention rate: 0% (compared to 87.5% in 2024)
• All 12 students in the 2023 cohort failed to be retained in first period
• This represents a complete breakdown of the retention system

FINANCIAL IMPACT:
• Lost tuition revenue from 12 students
• Wasted recruitment and admission resources
• Potential damage to program reputation and accreditation

STUDENT OUTCOMES:
• High dropout rates across all categories
• Significant personal and academic costs to affected students
• Potential long-term career impact for these individuals

URGENCY LEVEL: CRITICAL
• Immediate intervention required to prevent recurrence
• Comprehensive review of 2023 processes needed
• Emergency retention protocols must be implemented

NEXT STEPS:
1. Immediate investigation into 2023-specific factors
2. Implementation of early warning systems
3. Enhanced student support services
4. Regular monitoring and intervention protocols

Report Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return summary


def main():
    """Run the complete 2023 retention crisis analysis"""
    
    analyzer = RetentionCrisisAnalyzer()
    
    if not analyzer.load_data():
        return
    
    # Perform comprehensive analysis
    crisis_impact, outcomes = analyzer.analyze_2023_crisis()
    
    # Identify root causes
    causes = analyzer.identify_root_causes()
    
    # Generate recommendations
    recommendations = analyzer.generate_recommendations()
    
    # Create visualizations
    analyzer.create_crisis_visualization()
    
    # Generate executive summary
    executive_summary = analyzer.generate_executive_summary()
    
    # Save executive summary
    with open('executive_summary_2023_crisis.txt', 'w', encoding='utf-8') as f:
        f.write(executive_summary)
    
    print(executive_summary)
    print(f"\n✅ Complete crisis analysis saved to 'executive_summary_2023_crisis.txt'")


if __name__ == "__main__":
    main()
