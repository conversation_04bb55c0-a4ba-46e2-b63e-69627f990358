#!/usr/bin/env python3
"""
Quick runner script for student retention analysis
"""

from student_retention_analysis import StudentRetentionAnalyzer
import sys

def quick_analysis():
    """Run a quick retention analysis"""
    print("🚀 Running Quick Student Retention Analysis...")
    
    try:
        # Initialize and run analysis
        analyzer = StudentRetentionAnalyzer()
        
        # Load and merge data
        if not analyzer.load_data():
            print("❌ Error loading data")
            return False
            
        analyzer.merge_datasets()
        
        # Generate report
        report = analyzer.generate_comprehensive_report()
        
        # Print summary
        analyzer.print_summary_report(report)
        
        # Save files
        analyzer.save_report(report)
        analyzer.export_follow_up_lists(report['recommendations'])
        
        print("\n✅ Quick analysis completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        return False

def detailed_analysis():
    """Run detailed analysis with visualizations"""
    print("📊 Running Detailed Student Retention Analysis...")
    
    try:
        # Run the main analysis
        from student_retention_analysis import main
        main()
        return True
        
    except Exception as e:
        print(f"❌ Error during detailed analysis: {e}")
        return False

if __name__ == "__main__":
    print("Student Retention Analysis Runner")
    print("=" * 40)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        quick_analysis()
    else:
        detailed_analysis()
