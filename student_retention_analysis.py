#!/usr/bin/env python3
"""
Student Retention Analysis and Follow-up System
Analyzes student retention patterns and generates follow-up recommendations
"""

import pandas as pd
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")


class StudentRetentionAnalyzer:
    """Main class for student retention analysis and follow-up"""
    
    def __init__(self, csv_file: str = "casos_laboratorio_clinico.csv", 
                 api_file: str = "api_response.json"):
        """
        Initialize the analyzer with data files
        
        Args:
            csv_file: Path to CSV file with retention data
            api_file: Path to API response JSON file
        """
        self.csv_file = csv_file
        self.api_file = api_file
        self.csv_data = None
        self.api_data = None
        self.merged_data = None
        
        # Estado mappings for better understanding
        self.estado_mapping = {
            0: "No Activo",
            1: "Eliminado", 
            3: "Regular",
            4: "Abandono de Estudios",
            7: "Titulado",
            8: "Aprobado",
            12: "Retiro Temporal",
            13: "Egresado",
            1001: "Renuncia"
        }
        
        # Risk categories
        self.high_risk_states = [1, 4, 1001]  # Eliminado, Abandono, Renuncia
        self.medium_risk_states = [0, 12]     # No Activo, Retiro Temporal
        self.success_states = [7, 8, 13]      # Titulado, Aprobado, Egresado
        self.active_states = [3]              # Regular
        
    def load_data(self) -> bool:
        """Load and prepare data from both sources"""
        try:
            # Load CSV data
            print("Loading CSV data...")
            self.csv_data = pd.read_csv(self.csv_file, sep=';')
            print(f"CSV data loaded: {len(self.csv_data)} records")
            
            # Load API data
            print("Loading API data...")
            with open(self.api_file, 'r', encoding='utf-8') as f:
                self.api_data = json.load(f)
            print(f"API data loaded: {len(self.api_data)} records")
            
            # Convert API data to DataFrame
            self.api_df = pd.DataFrame(self.api_data)
            
            return True
            
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def merge_datasets(self) -> pd.DataFrame:
        """Merge CSV and API data for comprehensive analysis"""
        try:
            # Prepare CSV data
            csv_prep = self.csv_data.copy()
            csv_prep['N_DOC'] = csv_prep['N_DOC'].astype(str)
            
            # Prepare API data
            api_prep = self.api_df.copy()
            api_prep['rut'] = api_prep['rut'].astype(str)
            
            # Merge datasets
            merged = pd.merge(
                csv_prep, 
                api_prep, 
                left_on='N_DOC', 
                right_on='rut', 
                how='left'
            )
            
            self.merged_data = merged
            print(f"Merged dataset created: {len(merged)} records")
            
            return merged
            
        except Exception as e:
            print(f"Error merging datasets: {e}")
            return pd.DataFrame()
    
    def analyze_retention_patterns(self) -> Dict[str, Any]:
        """Analyze retention patterns from the data"""
        if self.merged_data is None:
            print("No merged data available. Run merge_datasets() first.")
            return {}
        
        analysis = {}
        
        # Basic statistics
        total_students = len(self.merged_data)
        analysis['total_students'] = total_students
        
        # Retention by RET columns (from CSV)
        ret_analysis = {}
        for col in ['RET_1', 'RET_2', 'RET_3']:
            if col in self.merged_data.columns:
                ret_analysis[col] = {
                    'retained': int(self.merged_data[col].sum()),
                    'retention_rate': float(self.merged_data[col].mean() * 100)
                }
        analysis['retention_by_period'] = ret_analysis
        
        # Estado analysis (from API)
        estado_counts = self.merged_data['estado'].value_counts().to_dict()
        estado_analysis = {}
        
        for estado_id, count in estado_counts.items():
            if pd.notna(estado_id):
                estado_text = self.estado_mapping.get(int(estado_id), f"Unknown ({estado_id})")
                estado_analysis[estado_text] = {
                    'count': int(count),
                    'percentage': float(count / total_students * 100)
                }
        
        analysis['estado_distribution'] = estado_analysis
        
        # Risk categorization
        risk_analysis = self.categorize_risk()
        analysis['risk_categories'] = risk_analysis
        
        # Gender analysis
        gender_analysis = self.analyze_by_gender()
        analysis['gender_analysis'] = gender_analysis
        
        # Cohort analysis
        cohort_analysis = self.analyze_by_cohort()
        analysis['cohort_analysis'] = cohort_analysis
        
        return analysis
    
    def categorize_risk(self) -> Dict[str, Any]:
        """Categorize students by retention risk"""
        if self.merged_data is None:
            return {}
        
        risk_categories = {
            'high_risk': 0,
            'medium_risk': 0, 
            'success': 0,
            'active': 0,
            'unknown': 0
        }
        
        for _, row in self.merged_data.iterrows():
            estado = row.get('estado')
            if pd.isna(estado):
                risk_categories['unknown'] += 1
            elif int(estado) in self.high_risk_states:
                risk_categories['high_risk'] += 1
            elif int(estado) in self.medium_risk_states:
                risk_categories['medium_risk'] += 1
            elif int(estado) in self.success_states:
                risk_categories['success'] += 1
            elif int(estado) in self.active_states:
                risk_categories['active'] += 1
            else:
                risk_categories['unknown'] += 1
        
        # Convert to percentages
        total = len(self.merged_data)
        risk_percentages = {k: (v/total)*100 for k, v in risk_categories.items()}
        
        return {
            'counts': risk_categories,
            'percentages': risk_percentages
        }
    
    def analyze_by_gender(self) -> Dict[str, Any]:
        """Analyze retention patterns by gender"""
        if self.merged_data is None:
            return {}
        
        gender_analysis = {}
        
        for gender in self.merged_data['SEXO'].unique():
            if pd.notna(gender):
                gender_data = self.merged_data[self.merged_data['SEXO'] == gender]
                
                # Retention rates by period
                retention_rates = {}
                for col in ['RET_1', 'RET_2', 'RET_3']:
                    if col in gender_data.columns:
                        retention_rates[col] = float(gender_data[col].mean() * 100)
                
                # Estado distribution
                estado_dist = gender_data['estado'].value_counts(normalize=True).to_dict()
                estado_dist = {str(k): float(v*100) for k, v in estado_dist.items() if pd.notna(k)}
                
                gender_analysis[gender] = {
                    'count': len(gender_data),
                    'retention_rates': retention_rates,
                    'estado_distribution': estado_dist
                }
        
        return gender_analysis
    
    def analyze_by_cohort(self) -> Dict[str, Any]:
        """Analyze retention patterns by cohort/year"""
        if self.merged_data is None:
            return {}
        
        cohort_analysis = {}
        
        for year in self.merged_data['AÑO_PROCESO_SIES'].unique():
            if pd.notna(year):
                year_data = self.merged_data[self.merged_data['AÑO_PROCESO_SIES'] == year]
                
                # Retention rates by period
                retention_rates = {}
                for col in ['RET_1', 'RET_2', 'RET_3']:
                    if col in year_data.columns:
                        retention_rates[col] = float(year_data[col].mean() * 100)
                
                cohort_analysis[str(year)] = {
                    'count': len(year_data),
                    'retention_rates': retention_rates
                }
        
        return cohort_analysis

    def identify_at_risk_students(self) -> pd.DataFrame:
        """Identify students who need immediate follow-up"""
        if self.merged_data is None:
            return pd.DataFrame()

        # Students in high-risk categories
        at_risk = self.merged_data[
            self.merged_data['estado'].isin(self.high_risk_states + self.medium_risk_states)
        ].copy()

        # Add risk level
        at_risk['risk_level'] = at_risk['estado'].apply(
            lambda x: 'HIGH' if x in self.high_risk_states else 'MEDIUM'
        )

        # Add estado text
        at_risk['estado_texto_mapped'] = at_risk['estado'].map(self.estado_mapping)

        # Select relevant columns for follow-up
        follow_up_columns = [
            'N_DOC', 'SEXO', 'NOMBRE_CARRERA', 'COHORTE_ESTUDIANTE',
            'AÑO_PROCESO_SIES', 'estado', 'estado_texto_mapped', 'risk_level',
            'periodo_inicio', 'RET_1', 'RET_2', 'RET_3'
        ]

        available_columns = [col for col in follow_up_columns if col in at_risk.columns]
        at_risk_final = at_risk[available_columns].copy()

        return at_risk_final.sort_values(['risk_level', 'estado'], ascending=[False, True])

    def generate_follow_up_recommendations(self) -> Dict[str, List[Dict]]:
        """Generate specific follow-up recommendations for different student groups"""
        at_risk_students = self.identify_at_risk_students()

        recommendations = {
            'immediate_action': [],
            'monitoring_required': [],
            'success_stories': []
        }

        # High-risk students (immediate action needed)
        high_risk = at_risk_students[at_risk_students['risk_level'] == 'HIGH']
        for _, student in high_risk.iterrows():
            recommendations['immediate_action'].append({
                'rut': student['N_DOC'],
                'estado': student.get('estado_texto_mapped', 'Unknown'),
                'action': self._get_action_for_estado(student.get('estado')),
                'priority': 'URGENT',
                'contact_method': 'Phone + Email + In-person meeting'
            })

        # Medium-risk students (monitoring required)
        medium_risk = at_risk_students[at_risk_students['risk_level'] == 'MEDIUM']
        for _, student in medium_risk.iterrows():
            recommendations['monitoring_required'].append({
                'rut': student['N_DOC'],
                'estado': student.get('estado_texto_mapped', 'Unknown'),
                'action': self._get_action_for_estado(student.get('estado')),
                'priority': 'HIGH',
                'contact_method': 'Email + Phone follow-up'
            })

        # Success stories (for best practices)
        if self.merged_data is not None:
            success_students = self.merged_data[
                self.merged_data['estado'].isin(self.success_states)
            ]
            for _, student in success_students.head(5).iterrows():
                recommendations['success_stories'].append({
                    'rut': student['N_DOC'],
                    'estado': student.get('estado_texto', 'Success'),
                    'note': 'Study success factors for replication'
                })

        return recommendations

    def _get_action_for_estado(self, estado: int) -> str:
        """Get specific action recommendation based on student estado"""
        action_map = {
            0: "Contact immediately - student inactive, investigate reasons and offer re-enrollment support",
            1: "Critical intervention needed - student eliminated, explore readmission possibilities",
            4: "Urgent counseling required - student abandoned studies, provide career guidance and support",
            12: "Follow up on temporary withdrawal - check return plans and provide transition support",
            1001: "Exit interview and feedback - understand resignation reasons for program improvement"
        }
        return action_map.get(estado, "Monitor closely and provide general academic support")

    def create_visualizations(self, save_plots: bool = True) -> None:
        """Create comprehensive visualizations of retention analysis"""
        if self.merged_data is None:
            print("No data available for visualization")
            return

        # Set up the plotting area
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Student Retention Analysis Dashboard', fontsize=16, fontweight='bold')

        # 1. Estado Distribution
        estado_counts = self.merged_data['estado'].value_counts()
        estado_labels = [self.estado_mapping.get(int(k), f"Estado {k}") for k in estado_counts.index if pd.notna(k)]

        axes[0, 0].pie(estado_counts.values, labels=estado_labels, autopct='%1.1f%%', startangle=90)
        axes[0, 0].set_title('Distribution by Estado')

        # 2. Retention by Period
        retention_data = []
        for col in ['RET_1', 'RET_2', 'RET_3']:
            if col in self.merged_data.columns:
                retention_data.append(self.merged_data[col].mean() * 100)

        if retention_data:
            periods = ['Period 1', 'Period 2', 'Period 3'][:len(retention_data)]
            axes[0, 1].bar(periods, retention_data, color=['#1f77b4', '#ff7f0e', '#2ca02c'])
            axes[0, 1].set_title('Retention Rate by Period')
            axes[0, 1].set_ylabel('Retention Rate (%)')
            axes[0, 1].set_ylim(0, 100)

        # 3. Gender Distribution
        gender_counts = self.merged_data['SEXO'].value_counts()
        axes[0, 2].pie(gender_counts.values, labels=gender_counts.index, autopct='%1.1f%%')
        axes[0, 2].set_title('Distribution by Gender')

        # 4. Risk Categories
        risk_data = self.categorize_risk()
        risk_counts = risk_data['counts']
        risk_labels = list(risk_counts.keys())
        risk_values = list(risk_counts.values())

        colors = ['#d62728', '#ff7f0e', '#2ca02c', '#1f77b4', '#9467bd']
        axes[1, 0].bar(risk_labels, risk_values, color=colors)
        axes[1, 0].set_title('Students by Risk Category')
        axes[1, 0].set_ylabel('Number of Students')
        axes[1, 0].tick_params(axis='x', rotation=45)

        # 5. Cohort Analysis
        cohort_data = self.analyze_by_cohort()
        if cohort_data:
            cohorts = list(cohort_data.keys())
            cohort_counts = [cohort_data[c]['count'] for c in cohorts]

            axes[1, 1].bar(cohorts, cohort_counts, color='#17becf')
            axes[1, 1].set_title('Students by Cohort/Year')
            axes[1, 1].set_ylabel('Number of Students')

        # 6. Retention Trend
        if len(retention_data) > 1:
            axes[1, 2].plot(periods, retention_data, marker='o', linewidth=2, markersize=8)
            axes[1, 2].set_title('Retention Trend Over Periods')
            axes[1, 2].set_ylabel('Retention Rate (%)')
            axes[1, 2].set_ylim(0, 100)
            axes[1, 2].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_plots:
            plt.savefig('student_retention_dashboard.png', dpi=300, bbox_inches='tight')
            print("Dashboard saved as 'student_retention_dashboard.png'")

        plt.show()

    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate a comprehensive retention analysis report"""
        print("Generating comprehensive retention analysis report...")

        # Perform all analyses
        retention_analysis = self.analyze_retention_patterns()
        at_risk_students = self.identify_at_risk_students()
        recommendations = self.generate_follow_up_recommendations()

        # Create summary report
        report = {
            'report_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_students': retention_analysis.get('total_students', 0),
                'at_risk_count': len(at_risk_students),
                'high_risk_count': len([r for r in recommendations['immediate_action']]),
                'medium_risk_count': len([r for r in recommendations['monitoring_required']]),
                'success_count': len([r for r in recommendations['success_stories']])
            },
            'detailed_analysis': retention_analysis,
            'at_risk_students': at_risk_students.to_dict('records') if not at_risk_students.empty else [],
            'recommendations': recommendations
        }

        return report

    def save_report(self, report: Dict[str, Any], filename: str = None) -> str:
        """Save the comprehensive report to JSON file"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'student_retention_report_{timestamp}.json'

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)

            print(f"Report saved to: {filename}")
            return filename

        except Exception as e:
            print(f"Error saving report: {e}")
            return ""

    def export_follow_up_lists(self, recommendations: Dict[str, List[Dict]]) -> None:
        """Export follow-up lists to CSV files for action"""
        try:
            # High-risk students for immediate action
            if recommendations['immediate_action']:
                immediate_df = pd.DataFrame(recommendations['immediate_action'])
                immediate_df.to_csv('immediate_action_students.csv', index=False)
                print("Immediate action list saved to: immediate_action_students.csv")

            # Medium-risk students for monitoring
            if recommendations['monitoring_required']:
                monitoring_df = pd.DataFrame(recommendations['monitoring_required'])
                monitoring_df.to_csv('monitoring_required_students.csv', index=False)
                print("Monitoring list saved to: monitoring_required_students.csv")

            # At-risk students summary
            at_risk_students = self.identify_at_risk_students()
            if not at_risk_students.empty:
                at_risk_students.to_csv('at_risk_students_detailed.csv', index=False)
                print("Detailed at-risk list saved to: at_risk_students_detailed.csv")

        except Exception as e:
            print(f"Error exporting follow-up lists: {e}")

    def print_summary_report(self, report: Dict[str, Any]) -> None:
        """Print a formatted summary report to console"""
        print("\n" + "="*80)
        print("STUDENT RETENTION ANALYSIS SUMMARY REPORT")
        print("="*80)
        print(f"Report Date: {report['report_date']}")
        print(f"Total Students Analyzed: {report['summary']['total_students']}")

        print("\n📊 RETENTION OVERVIEW:")
        print("-" * 40)

        # Risk distribution
        print(f"🔴 High Risk (Immediate Action): {report['summary']['high_risk_count']} students")
        print(f"🟡 Medium Risk (Monitoring): {report['summary']['medium_risk_count']} students")
        print(f"🟢 Success Stories: {report['summary']['success_count']} students")

        # Estado distribution
        if 'estado_distribution' in report['detailed_analysis']:
            print(f"\n📈 STUDENT STATUS DISTRIBUTION:")
            print("-" * 40)
            for estado, data in report['detailed_analysis']['estado_distribution'].items():
                print(f"{estado}: {data['count']} students ({data['percentage']:.1f}%)")

        # Retention by period
        if 'retention_by_period' in report['detailed_analysis']:
            print(f"\n📅 RETENTION BY PERIOD:")
            print("-" * 40)
            for period, data in report['detailed_analysis']['retention_by_period'].items():
                print(f"{period}: {data['retained']} students retained ({data['retention_rate']:.1f}%)")

        # Gender analysis
        if 'gender_analysis' in report['detailed_analysis']:
            print(f"\n👥 GENDER ANALYSIS:")
            print("-" * 40)
            for gender, data in report['detailed_analysis']['gender_analysis'].items():
                print(f"{gender}: {data['count']} students")

        print("\n🎯 IMMEDIATE ACTIONS REQUIRED:")
        print("-" * 40)
        if report['recommendations']['immediate_action']:
            for i, action in enumerate(report['recommendations']['immediate_action'][:5], 1):
                print(f"{i}. RUT {action['rut']}: {action['estado']} - {action['action'][:60]}...")
            if len(report['recommendations']['immediate_action']) > 5:
                print(f"... and {len(report['recommendations']['immediate_action']) - 5} more students")
        else:
            print("No students requiring immediate action.")

        print("\n" + "="*80)


def main():
    """Main function to run the complete retention analysis"""
    print("🎓 Student Retention Analysis and Follow-up System")
    print("=" * 60)

    # Initialize analyzer
    analyzer = StudentRetentionAnalyzer()

    # Load data
    if not analyzer.load_data():
        print("❌ Failed to load data. Exiting.")
        return

    # Merge datasets
    merged_data = analyzer.merge_datasets()
    if merged_data.empty:
        print("❌ Failed to merge datasets. Exiting.")
        return

    # Generate comprehensive report
    report = analyzer.generate_comprehensive_report()

    # Print summary to console
    analyzer.print_summary_report(report)

    # Save detailed report
    report_file = analyzer.save_report(report)

    # Export follow-up lists
    analyzer.export_follow_up_lists(report['recommendations'])

    # Create visualizations
    print("\n📊 Generating visualizations...")
    analyzer.create_visualizations(save_plots=True)

    print(f"\n✅ Analysis complete! Check the following files:")
    print(f"   📄 Detailed report: {report_file}")
    print(f"   📊 Dashboard: student_retention_dashboard.png")
    print(f"   📋 Action lists: immediate_action_students.csv, monitoring_required_students.csv")
    print(f"   📈 Detailed data: at_risk_students_detailed.csv")


if __name__ == "__main__":
    main()
