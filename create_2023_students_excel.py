#!/usr/bin/env python3
"""
Create Excel file with detailed information about 2023 students
"""

import pandas as pd
import json
from datetime import datetime

def create_2023_students_excel():
    """Create comprehensive Excel file for 2023 students"""
    
    print("📊 Creating Excel file for 2023 students...")
    
    # Load CSV data
    csv_data = pd.read_csv("casos_laboratorio_clinico.csv", sep=';')
    
    # Load API data
    with open("api_response.json", 'r', encoding='utf-8') as f:
        api_data = json.load(f)
    api_df = pd.DataFrame(api_data)
    
    # Load retention report
    with open("student_retention_report_20250820_113722.json", 'r', encoding='utf-8') as f:
        report_data = json.load(f)
    
    # Filter 2023 students from CSV
    students_2023_csv = csv_data[csv_data['AÑO_PROCESO_SIES'] == 2023].copy()
    
    print(f"Found {len(students_2023_csv)} students from 2023 in CSV data")
    
    # Prepare CSV data for merging
    students_2023_csv['N_DOC'] = students_2023_csv['N_DOC'].astype(str)
    
    # Prepare API data for merging
    api_df['rut'] = api_df['rut'].astype(str)
    
    # Merge CSV and API data
    merged_2023 = pd.merge(
        students_2023_csv,
        api_df,
        left_on='N_DOC',
        right_on='rut',
        how='left'
    )
    
    print(f"Merged data: {len(merged_2023)} records")
    
    # Add estado mapping
    estado_mapping = {
        0: "No Activo",
        1: "Eliminado", 
        3: "Regular",
        4: "Abandono de Estudios",
        7: "Titulado",
        8: "Aprobado",
        12: "Retiro Temporal",
        13: "Egresado",
        1001: "Renuncia"
    }
    
    merged_2023['estado_descripcion'] = merged_2023['estado'].map(estado_mapping)
    
    # Add risk categorization
    high_risk_states = [1, 4, 1001]  # Eliminado, Abandono, Renuncia
    medium_risk_states = [0, 12]     # No Activo, Retiro Temporal
    success_states = [7, 8, 13]      # Titulado, Aprobado, Egresado
    active_states = [3]              # Regular
    
    def categorize_risk(estado):
        if pd.isna(estado):
            return "Sin Información"
        elif int(estado) in high_risk_states:
            return "ALTO RIESGO"
        elif int(estado) in medium_risk_states:
            return "RIESGO MEDIO"
        elif int(estado) in success_states:
            return "EXITOSO"
        elif int(estado) in active_states:
            return "ACTIVO"
        else:
            return "DESCONOCIDO"
    
    merged_2023['categoria_riesgo'] = merged_2023['estado'].apply(categorize_risk)
    
    # Add retention analysis
    merged_2023['total_periodos_retenido'] = (
        merged_2023['RET_1'] + merged_2023['RET_2'] + merged_2023['RET_3']
    )
    
    # Calculate time in program
    merged_2023['fecha_inicio'] = pd.to_datetime(merged_2023['periodo_inicio'] + '.1', format='%Y.%m', errors='coerce')
    merged_2023['tiempo_en_programa_meses'] = (
        (datetime.now() - merged_2023['fecha_inicio']).dt.days / 30.44
    ).round(1)
    
    # Select and organize columns for Excel
    excel_columns = [
        'N_DOC',
        'SEXO', 
        'NOMBRE_CARRERA',
        'COHORTE_ESTUDIANTE',
        'AÑO_PROCESO_SIES',
        'periodo_inicio',
        'tiempo_en_programa_meses',
        'RET_1',
        'RET_2', 
        'RET_3',
        'total_periodos_retenido',
        'estado',
        'estado_descripcion',
        'categoria_riesgo',
        'nota_final',
        'fecha_modificacion',
        'id_carrera',
        'codigo_carrera',
        'version',
        'titulo'
    ]
    
    # Filter available columns
    available_columns = [col for col in excel_columns if col in merged_2023.columns]
    excel_data = merged_2023[available_columns].copy()
    
    # Sort by risk category and RUT
    risk_order = {'ALTO RIESGO': 1, 'RIESGO MEDIO': 2, 'Sin Información': 3, 'ACTIVO': 4, 'EXITOSO': 5, 'DESCONOCIDO': 6}
    excel_data['risk_sort'] = excel_data['categoria_riesgo'].map(risk_order)
    excel_data = excel_data.sort_values(['risk_sort', 'N_DOC']).drop('risk_sort', axis=1)
    
    # Create Excel file with multiple sheets
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'estudiantes_2023_detallado_{timestamp}.xlsx'
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        
        # Main sheet with all student data
        excel_data.to_excel(writer, sheet_name='Estudiantes_2023', index=False)
        
        # Summary sheet
        summary_data = create_summary_sheet(excel_data)
        summary_data.to_excel(writer, sheet_name='Resumen', index=False)
        
        # Risk analysis sheet
        risk_analysis = create_risk_analysis_sheet(excel_data)
        risk_analysis.to_excel(writer, sheet_name='Analisis_Riesgo', index=False)
        
        # Retention timeline sheet
        retention_timeline = create_retention_timeline_sheet(excel_data)
        retention_timeline.to_excel(writer, sheet_name='Cronologia_Retencion', index=False)
    
    print(f"✅ Excel file created: {filename}")
    print(f"📊 Total 2023 students: {len(excel_data)}")
    print(f"📋 Sheets created: Estudiantes_2023, Resumen, Analisis_Riesgo, Cronologia_Retencion")
    
    # Print summary statistics
    print(f"\n📈 RESUMEN ESTUDIANTES 2023:")
    print("-" * 40)
    risk_summary = excel_data['categoria_riesgo'].value_counts()
    for risk, count in risk_summary.items():
        percentage = (count / len(excel_data)) * 100
        print(f"{risk}: {count} estudiantes ({percentage:.1f}%)")
    
    return filename, excel_data

def create_summary_sheet(data):
    """Create summary statistics sheet"""
    
    summary_stats = []
    
    # Basic statistics
    summary_stats.append(['Métrica', 'Valor'])
    summary_stats.append(['Total Estudiantes 2023', len(data)])
    summary_stats.append(['', ''])
    
    # Gender distribution
    summary_stats.append(['DISTRIBUCIÓN POR GÉNERO', ''])
    gender_counts = data['SEXO'].value_counts()
    for gender, count in gender_counts.items():
        percentage = (count / len(data)) * 100
        summary_stats.append([f'{gender}', f'{count} ({percentage:.1f}%)'])
    summary_stats.append(['', ''])
    
    # Risk distribution
    summary_stats.append(['DISTRIBUCIÓN POR RIESGO', ''])
    risk_counts = data['categoria_riesgo'].value_counts()
    for risk, count in risk_counts.items():
        percentage = (count / len(data)) * 100
        summary_stats.append([f'{risk}', f'{count} ({percentage:.1f}%)'])
    summary_stats.append(['', ''])
    
    # Retention statistics
    summary_stats.append(['ESTADÍSTICAS DE RETENCIÓN', ''])
    summary_stats.append(['RET_1 Promedio', f"{data['RET_1'].mean():.1f}%"])
    summary_stats.append(['RET_2 Promedio', f"{data['RET_2'].mean():.1f}%"])
    summary_stats.append(['RET_3 Promedio', f"{data['RET_3'].mean():.1f}%"])
    summary_stats.append(['Estudiantes con RET_1=1', data['RET_1'].sum()])
    summary_stats.append(['Estudiantes con RET_1=0', len(data) - data['RET_1'].sum()])
    
    return pd.DataFrame(summary_stats)

def create_risk_analysis_sheet(data):
    """Create detailed risk analysis sheet"""
    
    risk_analysis = []
    
    # Header
    risk_analysis.append(['RUT', 'Género', 'Estado', 'Categoría Riesgo', 'RET_1', 'RET_2', 'RET_3', 'Tiempo en Programa (meses)', 'Recomendación'])
    
    # Risk-based recommendations
    def get_recommendation(row):
        if row['categoria_riesgo'] == 'ALTO RIESGO':
            if row['estado_descripcion'] == 'Renuncia':
                return 'Entrevista de salida urgente - Analizar causas de renuncia'
            elif row['estado_descripcion'] == 'Abandono de Estudios':
                return 'Intervención crítica - Ofrecer apoyo académico y personal'
            elif row['estado_descripcion'] == 'Eliminado':
                return 'Revisar posibilidades de readmisión con apoyo adicional'
            else:
                return 'Contacto inmediato - Evaluación integral de situación'
        elif row['categoria_riesgo'] == 'RIESGO MEDIO':
            return 'Monitoreo cercano - Contacto mensual y apoyo preventivo'
        elif row['categoria_riesgo'] == 'Sin Información':
            return 'Investigar estado actual - Contactar para actualizar información'
        else:
            return 'Seguimiento estándar'
    
    # Add student data with recommendations
    for _, row in data.iterrows():
        recommendation = get_recommendation(row)
        risk_analysis.append([
            row['N_DOC'],
            row['SEXO'],
            row.get('estado_descripcion', 'Sin información'),
            row['categoria_riesgo'],
            row['RET_1'],
            row['RET_2'],
            row['RET_3'],
            row.get('tiempo_en_programa_meses', 'N/A'),
            recommendation
        ])
    
    return pd.DataFrame(risk_analysis[1:], columns=risk_analysis[0])

def create_retention_timeline_sheet(data):
    """Create retention timeline analysis"""
    
    timeline_data = []
    
    # Header
    timeline_data.append(['RUT', 'Género', 'Fecha Inicio', 'RET_1', 'RET_2', 'RET_3', 'Estado Final', 'Patrón de Retención'])
    
    # Analyze retention patterns
    def get_retention_pattern(row):
        ret1, ret2, ret3 = row['RET_1'], row['RET_2'], row['RET_3']
        
        if ret1 == 0 and ret2 == 0 and ret3 == 0:
            return 'Pérdida inmediata (0-0-0)'
        elif ret1 == 1 and ret2 == 0 and ret3 == 0:
            return 'Pérdida en segundo período (1-0-0)'
        elif ret1 == 1 and ret2 == 1 and ret3 == 0:
            return 'Pérdida en tercer período (1-1-0)'
        elif ret1 == 1 and ret2 == 1 and ret3 == 1:
            return 'Retención completa (1-1-1)'
        else:
            return f'Patrón irregular ({ret1}-{ret2}-{ret3})'
    
    # Add timeline data
    for _, row in data.iterrows():
        pattern = get_retention_pattern(row)
        timeline_data.append([
            row['N_DOC'],
            row['SEXO'],
            row.get('periodo_inicio', 'N/A'),
            row['RET_1'],
            row['RET_2'],
            row['RET_3'],
            row.get('estado_descripcion', 'Sin información'),
            pattern
        ])
    
    return pd.DataFrame(timeline_data[1:], columns=timeline_data[0])

if __name__ == "__main__":
    filename, data = create_2023_students_excel()
    print(f"\n✅ Archivo Excel creado exitosamente: {filename}")
