{"report_date": "2025-08-20 11:35:40", "summary": {"total_students": 70, "at_risk_count": 28, "high_risk_count": 21, "medium_risk_count": 7, "success_count": 5}, "detailed_analysis": {"total_students": 70, "retention_by_period": {"RET_1": {"retained": 30, "retention_rate": 42.857142857142854}, "RET_2": {"retained": 0, "retention_rate": 0.0}, "RET_3": {"retained": 0, "retention_rate": 0.0}}, "estado_distribution": {"Regular": {"count": 27, "percentage": 38.57142857142858}, "Eliminado": {"count": 9, "percentage": 12.857142857142856}, "Titulado": {"count": 9, "percentage": 12.857142857142856}, "Renuncia": {"count": 7, "percentage": 10.0}, "No Activo": {"count": 6, "percentage": 8.571428571428571}, "Abandono de Estudios": {"count": 5, "percentage": 7.142857142857142}, "Aprobado": {"count": 3, "percentage": 4.285714285714286}, "Egresado": {"count": 1, "percentage": 1.4285714285714286}, "Retiro Temporal": {"count": 1, "percentage": 1.4285714285714286}}, "risk_categories": {"counts": {"high_risk": 21, "medium_risk": 7, "success": 13, "active": 27, "unknown": 2}, "percentages": {"high_risk": 30.0, "medium_risk": 10.0, "success": 18.571428571428573, "active": 38.57142857142858, "unknown": 2.857142857142857}}, "gender_analysis": {"Mujer": {"count": 59, "retention_rates": {"RET_1": 37.28813559322034, "RET_2": 0.0, "RET_3": 0.0}, "estado_distribution": {"3.0": 42.10526315789473, "1001.0": 12.280701754385964, "1.0": 10.526315789473683, "4.0": 8.771929824561402, "0.0": 8.771929824561402, "7.0": 8.771929824561402, "8.0": 5.263157894736842, "13.0": 1.7543859649122806, "12.0": 1.7543859649122806}}, "Hombre": {"count": 11, "retention_rates": {"RET_1": 72.72727272727273, "RET_2": 0.0, "RET_3": 0.0}, "estado_distribution": {"7.0": 36.36363636363637, "3.0": 27.27272727272727, "1.0": 27.27272727272727, "0.0": 9.090909090909092}}}, "cohort_analysis": {"2024": {"count": 16, "retention_rates": {"RET_1": 87.5, "RET_2": 0.0, "RET_3": 0.0}}, "2023": {"count": 12, "retention_rates": {"RET_1": 0.0, "RET_2": 0.0, "RET_3": 0.0}}, "2022": {"count": 42, "retention_rates": {"RET_1": 38.095238095238095, "RET_2": 0.0, "RET_3": 0.0}}}}, "at_risk_students": [{"N_DOC": "21226541", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2024, "estado": 0.0, "estado_texto_mapped": "No Activo", "risk_level": "MEDIUM", "periodo_inicio": "2024.1", "RET_1": 1, "RET_2": 0, "RET_3": 0}, {"N_DOC": "25025419", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 0.0, "estado_texto_mapped": "No Activo", "risk_level": "MEDIUM", "periodo_inicio": "2024.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "25025419", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 0.0, "estado_texto_mapped": "No Activo", "risk_level": "MEDIUM", "periodo_inicio": "2024.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "26489491", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 0.0, "estado_texto_mapped": "No Activo", "risk_level": "MEDIUM", "periodo_inicio": "2022.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "21338716", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 0.0, "estado_texto_mapped": "No Activo", "risk_level": "MEDIUM", "periodo_inicio": "2022.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "20937646", "SEXO": "Hombre", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 0.0, "estado_texto_mapped": "No Activo", "risk_level": "MEDIUM", "periodo_inicio": "2022.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "21229950", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 12.0, "estado_texto_mapped": "<PERSON><PERSON><PERSON>", "risk_level": "MEDIUM", "periodo_inicio": "2022.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "17938781", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1.0, "estado_texto_mapped": "Eliminado", "risk_level": "HIGH", "periodo_inicio": "2017.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "17938781", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1.0, "estado_texto_mapped": "Eliminado", "risk_level": "HIGH", "periodo_inicio": "2022.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "20545728", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1.0, "estado_texto_mapped": "Eliminado", "risk_level": "HIGH", "periodo_inicio": "2020.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "20545728", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1.0, "estado_texto_mapped": "Eliminado", "risk_level": "HIGH", "periodo_inicio": "2020.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "18790763", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1.0, "estado_texto_mapped": "Eliminado", "risk_level": "HIGH", "periodo_inicio": "2022.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "16926891", "SEXO": "Hombre", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1.0, "estado_texto_mapped": "Eliminado", "risk_level": "HIGH", "periodo_inicio": "2008.1", "RET_1": 1, "RET_2": 0, "RET_3": 0}, {"N_DOC": "21318394", "SEXO": "Hombre", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1.0, "estado_texto_mapped": "Eliminado", "risk_level": "HIGH", "periodo_inicio": "2018.1", "RET_1": 1, "RET_2": 0, "RET_3": 0}, {"N_DOC": "21318394", "SEXO": "Hombre", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1.0, "estado_texto_mapped": "Eliminado", "risk_level": "HIGH", "periodo_inicio": "2020.1", "RET_1": 1, "RET_2": 0, "RET_3": 0}, {"N_DOC": "16773344", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1.0, "estado_texto_mapped": "Eliminado", "risk_level": "HIGH", "periodo_inicio": "2022.1", "RET_1": 1, "RET_2": 0, "RET_3": 0}, {"N_DOC": "25752257", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2024, "estado": 4.0, "estado_texto_mapped": "Abandono de Estudios", "risk_level": "HIGH", "periodo_inicio": "2024.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "25915296", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2023, "estado": 4.0, "estado_texto_mapped": "Abandono de Estudios", "risk_level": "HIGH", "periodo_inicio": "2023.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "20545728", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 4.0, "estado_texto_mapped": "Abandono de Estudios", "risk_level": "HIGH", "periodo_inicio": "2022.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "19969900", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 4.0, "estado_texto_mapped": "Abandono de Estudios", "risk_level": "HIGH", "periodo_inicio": "2022.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "21378240", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 4.0, "estado_texto_mapped": "Abandono de Estudios", "risk_level": "HIGH", "periodo_inicio": "2022.1", "RET_1": 1, "RET_2": 0, "RET_3": 0}, {"N_DOC": "20416390", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2023, "estado": 1001.0, "estado_texto_mapped": "Renuncia", "risk_level": "HIGH", "periodo_inicio": "2023.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "21631008", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2023, "estado": 1001.0, "estado_texto_mapped": "Renuncia", "risk_level": "HIGH", "periodo_inicio": "2023.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "25025419", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1001.0, "estado_texto_mapped": "Renuncia", "risk_level": "HIGH", "periodo_inicio": "2022.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "25025419", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1001.0, "estado_texto_mapped": "Renuncia", "risk_level": "HIGH", "periodo_inicio": "2023.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "25025419", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1001.0, "estado_texto_mapped": "Renuncia", "risk_level": "HIGH", "periodo_inicio": "2023.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "20797697", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1001.0, "estado_texto_mapped": "Renuncia", "risk_level": "HIGH", "periodo_inicio": "2022.1", "RET_1": 0, "RET_2": 0, "RET_3": 0}, {"N_DOC": "20544387", "SEXO": "<PERSON><PERSON>", "NOMBRE_CARRERA": "Tecnico De Nivel Superior En Laboratorio Clinico Y Medicina Transfusional", "COHORTE_ESTUDIANTE": "Cohorte principal", "AÑO_PROCESO_SIES": 2022, "estado": 1001.0, "estado_texto_mapped": "Renuncia", "risk_level": "HIGH", "periodo_inicio": "2022.1", "RET_1": 1, "RET_2": 0, "RET_3": 0}], "recommendations": {"immediate_action": [{"rut": "17938781", "estado": "Eliminado", "action": "Critical intervention needed - student eliminated, explore readmission possibilities", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "17938781", "estado": "Eliminado", "action": "Critical intervention needed - student eliminated, explore readmission possibilities", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "20545728", "estado": "Eliminado", "action": "Critical intervention needed - student eliminated, explore readmission possibilities", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "20545728", "estado": "Eliminado", "action": "Critical intervention needed - student eliminated, explore readmission possibilities", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "18790763", "estado": "Eliminado", "action": "Critical intervention needed - student eliminated, explore readmission possibilities", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "16926891", "estado": "Eliminado", "action": "Critical intervention needed - student eliminated, explore readmission possibilities", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "21318394", "estado": "Eliminado", "action": "Critical intervention needed - student eliminated, explore readmission possibilities", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "21318394", "estado": "Eliminado", "action": "Critical intervention needed - student eliminated, explore readmission possibilities", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "16773344", "estado": "Eliminado", "action": "Critical intervention needed - student eliminated, explore readmission possibilities", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "25752257", "estado": "Abandono de Estudios", "action": "Urgent counseling required - student abandoned studies, provide career guidance and support", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "25915296", "estado": "Abandono de Estudios", "action": "Urgent counseling required - student abandoned studies, provide career guidance and support", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "20545728", "estado": "Abandono de Estudios", "action": "Urgent counseling required - student abandoned studies, provide career guidance and support", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "19969900", "estado": "Abandono de Estudios", "action": "Urgent counseling required - student abandoned studies, provide career guidance and support", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "21378240", "estado": "Abandono de Estudios", "action": "Urgent counseling required - student abandoned studies, provide career guidance and support", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "20416390", "estado": "Renuncia", "action": "Exit interview and feedback - understand resignation reasons for program improvement", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "21631008", "estado": "Renuncia", "action": "Exit interview and feedback - understand resignation reasons for program improvement", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "25025419", "estado": "Renuncia", "action": "Exit interview and feedback - understand resignation reasons for program improvement", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "25025419", "estado": "Renuncia", "action": "Exit interview and feedback - understand resignation reasons for program improvement", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "25025419", "estado": "Renuncia", "action": "Exit interview and feedback - understand resignation reasons for program improvement", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "20797697", "estado": "Renuncia", "action": "Exit interview and feedback - understand resignation reasons for program improvement", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}, {"rut": "20544387", "estado": "Renuncia", "action": "Exit interview and feedback - understand resignation reasons for program improvement", "priority": "URGENT", "contact_method": "Phone + Email + In-person meeting"}], "monitoring_required": [{"rut": "21226541", "estado": "No Activo", "action": "Contact immediately - student inactive, investigate reasons and offer re-enrollment support", "priority": "HIGH", "contact_method": "Email + Phone follow-up"}, {"rut": "25025419", "estado": "No Activo", "action": "Contact immediately - student inactive, investigate reasons and offer re-enrollment support", "priority": "HIGH", "contact_method": "Email + Phone follow-up"}, {"rut": "25025419", "estado": "No Activo", "action": "Contact immediately - student inactive, investigate reasons and offer re-enrollment support", "priority": "HIGH", "contact_method": "Email + Phone follow-up"}, {"rut": "26489491", "estado": "No Activo", "action": "Contact immediately - student inactive, investigate reasons and offer re-enrollment support", "priority": "HIGH", "contact_method": "Email + Phone follow-up"}, {"rut": "21338716", "estado": "No Activo", "action": "Contact immediately - student inactive, investigate reasons and offer re-enrollment support", "priority": "HIGH", "contact_method": "Email + Phone follow-up"}, {"rut": "20937646", "estado": "No Activo", "action": "Contact immediately - student inactive, investigate reasons and offer re-enrollment support", "priority": "HIGH", "contact_method": "Email + Phone follow-up"}, {"rut": "21229950", "estado": "<PERSON><PERSON><PERSON>", "action": "Follow up on temporary withdrawal - check return plans and provide transition support", "priority": "HIGH", "contact_method": "Email + Phone follow-up"}], "success_stories": [{"rut": "26489491", "estado": " Egresado", "note": "Study success factors for replication"}, {"rut": "21338716", "estado": "<PERSON><PERSON><PERSON><PERSON>", "note": "Study success factors for replication"}, {"rut": "21235638", "estado": "Aprobado", "note": "Study success factors for replication"}, {"rut": "21229950", "estado": "Aprobado", "note": "Study success factors for replication"}, {"rut": "20915217", "estado": "Aprobado", "note": "Study success factors for replication"}]}}