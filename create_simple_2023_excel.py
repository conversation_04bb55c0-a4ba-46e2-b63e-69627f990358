#!/usr/bin/env python3
"""
Create simple Excel file with 2023 students basic information
"""

import pandas as pd
import json

def create_simple_2023_excel():
    """Create simple Excel with key 2023 student information"""
    
    print("📊 Creating simple Excel file for 2023 students...")
    
    # Load CSV data
    csv_data = pd.read_csv("casos_laboratorio_clinico.csv", sep=';')
    
    # Load API data
    with open("api_response.json", 'r', encoding='utf-8') as f:
        api_data = json.load(f)
    api_df = pd.DataFrame(api_data)
    
    # Filter 2023 students from CSV
    students_2023 = csv_data[csv_data['AÑO_PROCESO_SIES'] == 2023].copy()
    
    # Prepare for merging
    students_2023['N_DOC'] = students_2023['N_DOC'].astype(str)
    api_df['rut'] = api_df['rut'].astype(str)
    
    # Merge with API data
    merged = pd.merge(
        students_2023,
        api_df[['rut', 'estado', 'estado_texto', 'periodo_inicio', 'nota_final']],
        left_on='N_DOC',
        right_on='rut',
        how='left'
    )
    
    # Create simple output with key columns
    simple_data = pd.DataFrame({
        'RUT': merged['N_DOC'],
        'Género': merged['SEXO'],
        'Carrera': 'Técnico Laboratorio Clínico',
        'Año_Ingreso': 2023,
        'Periodo_Inicio': merged['periodo_inicio'],
        'RET_1': merged['RET_1'],
        'RET_2': merged['RET_2'], 
        'RET_3': merged['RET_3'],
        'Estado_Actual': merged['estado_texto'].fillna('Sin información'),
        'Nota_Final': merged['nota_final'].fillna(''),
        'Observaciones': ''
    })
    
    # Add observations based on retention pattern
    def get_observation(row):
        if row['RET_1'] == 0:
            return 'No retenido en primer período - CRÍTICO'
        elif row['Estado_Actual'] in ['Renuncia', 'Abandono de Estudios', 'Eliminado']:
            return f'Estado de riesgo: {row["Estado_Actual"]}'
        elif row['Estado_Actual'] == 'Regular':
            return 'Estudiante activo'
        else:
            return 'Revisar estado actual'
    
    simple_data['Observaciones'] = simple_data.apply(get_observation, axis=1)
    
    # Save to Excel
    filename = 'estudiantes_2023_simple.xlsx'
    simple_data.to_excel(filename, index=False)
    
    print(f"✅ Excel simple creado: {filename}")
    print(f"📊 Total estudiantes: {len(simple_data)}")
    
    # Print summary
    print(f"\n📋 RESUMEN:")
    print(f"- Total estudiantes 2023: {len(simple_data)}")
    print(f"- Mujeres: {len(simple_data[simple_data['Género'] == 'Mujer'])}")
    print(f"- Hombres: {len(simple_data[simple_data['Género'] == 'Hombre'])}")
    print(f"- RET_1 = 0: {len(simple_data[simple_data['RET_1'] == 0])} estudiantes")
    print(f"- RET_1 = 1: {len(simple_data[simple_data['RET_1'] == 1])} estudiantes")
    
    return filename, simple_data

if __name__ == "__main__":
    filename, data = create_simple_2023_excel()
    print(f"\n✅ Archivo creado: {filename}")
    print("\n📄 Vista previa de los datos:")
    print(data.to_string(index=False))
