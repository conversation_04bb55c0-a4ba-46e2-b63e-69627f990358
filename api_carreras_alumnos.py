#!/usr/bin/env python3
"""
Script to query the carreras_alumnos API with RUTs from CSV file.
Reads N_DOC column from casos_laboratorio_clinico.csv and makes API request.
"""

import pandas as pd
import requests
import json
from urllib.parse import urlencode
from typing import List, Dict, Any


def read_ruts_from_csv(csv_file: str = "casos_laboratorio_clinico.csv") -> List[str]:
    """
    Read RUTs from the N_DOC column of the CSV file.
    
    Args:
        csv_file: Path to the CSV file
        
    Returns:
        List of RUT strings
    """
    try:
        # Read CSV with semicolon separator
        df = pd.read_csv(csv_file, sep=';')
        
        # Extract N_DOC column and convert to string
        ruts = df['N_DOC'].astype(str).tolist()
        
        print(f"Found {len(ruts)} RUTs in the CSV file")
        return ruts
        
    except FileNotFoundError:
        print(f"Error: CSV file '{csv_file}' not found")
        return []
    except KeyError:
        print("Error: 'N_DOC' column not found in CSV file")
        return []
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return []


def build_api_url(base_url: str, ruts: List[str]) -> str:
    """
    Build the API URL with RUT parameters.
    
    Args:
        base_url: Base API endpoint URL
        ruts: List of RUT strings
        
    Returns:
        Complete API URL with parameters
    """
    # Create parameters dictionary with multiple rut[] parameters
    params = [('rut[]', rut) for rut in ruts]
    
    # Build the query string
    query_string = urlencode(params)
    
    return f"{base_url}?{query_string}"


def query_carreras_alumnos_api(ruts: List[str], base_url: str = "/api/0/mufasa/carreras_alumnos") -> Dict[str, Any]:
    """
    Query the carreras_alumnos API with the provided RUTs.
    
    Args:
        ruts: List of RUT strings
        base_url: API endpoint (default: "/api/0/mufasa/carreras_alumnos")
        
    Returns:
        API response as dictionary
    """
    if not ruts:
        print("No RUTs provided for API query")
        return {}
    
    # Build complete URL
    api_url = build_api_url(base_url, ruts)
    
    print(f"Making API request to: {api_url}")
    print(f"Querying {len(ruts)} RUTs...")
    
    try:
        # Make the API request
        response = requests.get(api_url)
        
        # Check if request was successful
        response.raise_for_status()
        
        # Parse JSON response
        data = response.json()
        
        print(f"API request successful! Status code: {response.status_code}")
        return data
        
    except requests.exceptions.RequestException as e:
        print(f"Error making API request: {e}")
        return {}
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON response: {e}")
        return {}


def save_response_to_file(data: Dict[str, Any], filename: str = "api_response.json") -> None:
    """
    Save API response to a JSON file.
    
    Args:
        data: API response data
        filename: Output filename
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"Response saved to {filename}")
    except Exception as e:
        print(f"Error saving response to file: {e}")


def main():
    """Main function to execute the script."""
    print("=== Carreras Alumnos API Query Script ===")
    
    # Read RUTs from CSV
    ruts = read_ruts_from_csv()
    
    if not ruts:
        print("No RUTs found. Exiting.")
        return
    
    # Display first few RUTs for verification
    print(f"First 5 RUTs: {ruts[:5]}")
    
    # Query the API
    response_data = query_carreras_alumnos_api(ruts)
    
    if response_data:
        # Save response to file
        save_response_to_file(response_data)
        
        # Display summary
        print(f"\nResponse summary:")
        print(f"- Response type: {type(response_data)}")
        if isinstance(response_data, dict):
            print(f"- Keys in response: {list(response_data.keys())}")
        elif isinstance(response_data, list):
            print(f"- Number of items: {len(response_data)}")
    else:
        print("No data received from API")


if __name__ == "__main__":
    main()
